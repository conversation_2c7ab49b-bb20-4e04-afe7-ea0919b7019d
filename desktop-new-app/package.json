{"name": "desktop-new-app", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-table": "^8.21.3", "@types/dagre": "^0.7.53", "@types/keytar": "^4.4.0", "@types/react-router-dom": "^5.3.3", "@xyflow/react": "^12.8.1", "assert-plus": "^1.0.0", "better-sqlite3": "^12.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dagre": "^0.8.5", "docx": "^9.5.1", "docxtemplater": "^3.65.1", "electron-updater": "^6.3.9", "framer-motion": "^12.19.2", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "http-signature": "^1.4.0", "ifsc": "^1.1.8", "jspdf": "^3.0.1", "jsprim": "^2.0.2", "keytar": "^7.9.0", "lucide-react": "^0.522.0", "motion": "^12.19.2", "pdf-lib": "^1.17.1", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "sqlite3": "^5.1.7", "sshpk": "^1.18.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "uuid": "^11.1.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/better-sqlite3": "^7.6.13", "@types/node": "^22.15.32", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.3.4", "electron": "^35.5.1", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss-cli": "^0.1.2", "typescript": "^5.8.3", "vite": "^6.2.6"}}