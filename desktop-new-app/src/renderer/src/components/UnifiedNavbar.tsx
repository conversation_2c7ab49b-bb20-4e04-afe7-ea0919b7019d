import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
import {
  FiSun,
  FiMoon,
  FiHome,
  FiUpload,
  FiSettings,
  FiUser,
  FiSearch,
  FiBarChart,
  FiFileText
} from 'react-icons/fi'

interface UnifiedNavbarProps {
  title?: string
  showBackButton?: boolean
  customActions?: React.ReactNode
  className?: string
}

const UnifiedNavbar: React.FC<UnifiedNavbarProps> = ({
  title,
  showBackButton = false,
  customActions,
  className
}) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { isDark, toggleTheme } = useThemeContext()

  const navigationItems = [
    { icon: FiHome, label: 'Dashboard', path: '/dashboard' },
    { icon: FiUpload, label: 'Upload', path: '/upload' },
    { icon: FiSearch, label: 'Search', path: '/information-search' },
    { icon: <PERSON><PERSON><PERSON><PERSON><PERSON>, label: 'Stats', path: '/all-stats' }
  ]

  const isActivePath = (path: string): boolean => {
    return location.pathname === path || location.pathname.startsWith(path + '/')
  }

  return (
    <nav
      className={cn(
        'sticky top-0 z-50 w-full border-b backdrop-blur-md shadow-lg transition-all duration-300',
        isDark
          ? 'border-white/10 bg-slate-950/80 shadow-emerald-500/10'
          : 'border-gray-200/50 bg-white/80 shadow-blue-500/10',
        'hover:shadow-xl',
        isDark ? 'hover:shadow-emerald-500/20' : 'hover:shadow-blue-500/20',
        className
      )}
    >
      <div className="flex h-16 items-center justify-between px-6">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {showBackButton && (
            <button
              onClick={() => navigate(-1)}
              className={cn(
                'flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200',
                'hover:scale-105 active:scale-95',
                isDark ? 'hover:bg-white/10 text-white/80' : 'hover:bg-gray-100 text-gray-600'
              )}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          )}

          {title && (
            <h1 className={cn('text-xl font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
              {title}
            </h1>
          )}
        </div>

        {/* Center navigation */}
        <div className="hidden md:flex items-center space-x-1">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const isActive = isActivePath(item.path)

            return (
              <button
                key={item.path}
                onClick={() => navigate(item.path)}
                className={cn(
                  'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300',
                  'hover:scale-105 active:scale-95 group',
                  isActive
                    ? isDark
                      ? 'bg-emerald-600 text-white shadow-lg shadow-emerald-600/30 hover:shadow-emerald-600/40'
                      : 'bg-blue-500 text-white shadow-lg shadow-blue-500/30 hover:shadow-blue-500/40'
                    : isDark
                      ? 'text-white/70 hover:text-white hover:bg-white/10 hover:shadow-lg hover:shadow-emerald-500/20'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 hover:shadow-lg hover:shadow-blue-500/20'
                )}
              >
                <Icon className={cn(
                  'w-4 h-4 transition-all duration-300',
                  isActive
                    ? 'drop-shadow-sm'
                    : 'group-hover:drop-shadow-sm group-hover:scale-110'
                )} />
                <span className="hidden lg:inline">{item.label}</span>
              </button>
            )
          })}
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-3">
          {/* Custom actions */}
          {customActions}

          {/* Theme toggle */}
          <button
            onClick={toggleTheme}
            className={cn(
              'flex items-center justify-center w-9 h-9 rounded-lg transition-all duration-200',
              'hover:scale-105 active:scale-95',
              isDark
                ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30'
                : 'bg-blue-500/20 text-blue-600 hover:bg-blue-500/30'
            )}
          >
            {isDark ? <FiSun className="w-4 h-4" /> : <FiMoon className="w-4 h-4" />}
          </button>

          {/* Profile menu */}
          <button
            onClick={() => navigate('/profile')}
            className={cn(
              'flex items-center justify-center w-9 h-9 rounded-lg transition-all duration-200',
              'hover:scale-105 active:scale-95',
              isDark
                ? 'bg-white/10 text-white/80 hover:bg-white/20'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            )}
          >
            <FiUser className="w-4 h-4" />
          </button>

          {/* Settings */}
          <button
            onClick={() => navigate('/settings')}
            className={cn(
              'flex items-center justify-center w-9 h-9 rounded-lg transition-all duration-200',
              'hover:scale-105 active:scale-95',
              isDark
                ? 'bg-white/10 text-white/80 hover:bg-white/20'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            )}
          >
            <FiSettings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Mobile navigation */}
      <div className="md:hidden border-t border-border/50">
        <div className="flex items-center justify-around py-2">
          {navigationItems.slice(0, 4).map((item) => {
            const Icon = item.icon
            const isActive = isActivePath(item.path)

            return (
              <button
                key={item.path}
                onClick={() => navigate(item.path)}
                className={cn(
                  'flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200',
                  isActive
                    ? isDark
                      ? 'text-blue-400'
                      : 'text-blue-600'
                    : isDark
                      ? 'text-white/60'
                      : 'text-gray-500'
                )}
              >
                <Icon className="w-5 h-5" />
                <span className="text-xs">{item.label}</span>
              </button>
            )
          })}
        </div>
      </div>
    </nav>
  )
}

export default UnifiedNavbar
