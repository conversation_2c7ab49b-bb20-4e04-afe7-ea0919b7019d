// Browser-compatible IFSC service - removed Node.js dependencies

// Define the IFSC details interface based on the ifsc package
interface IFSCDetails {
  BANK: string
  IFSC: string
  BRANCH: string
  ADDRESS: string
  CONTACT: string
  CITY: string
  RTGS: boolean
  NEFT: boolean
  IMPS: boolean
  UPI: boolean
  DISTRICT: string
  STATE: string
  BANKCODE: string
  MICR?: string
}

// Enhanced bank data structure
interface EnrichedBankData {
  original_bank_name: string
  bank_name: string
  branch_name: string
  branch_address: string
  branch_state: string
  branch_city: string
  branch_district: string
  ifsc_code: string
  is_ifsc_valid: boolean
  enrichment_status: 'success' | 'invalid_ifsc' | 'api_error' | 'no_ifsc'
  enriched_at: string
}

// Transaction data interface (matching the backend structure)
interface TransactionData {
  receiver_ifsc?: string
  receiver_bank?: string
  [key: string]: any
}

class IFSCEnrichmentService {
  private cache: Map<string, IFSCDetails | null> = new Map()
  private readonly maxCacheSize = 1000
  private readonly cacheExpiryMs = 24 * 60 * 60 * 1000 // 24 hours

  /**
   * Validate an IFSC code (browser-compatible)
   */
  validateIFSC(ifscCode: string): boolean {
    try {
      if (!ifscCode || typeof ifscCode !== 'string') {
        return false
      }

      // Clean the IFSC code
      const cleanIFSC = ifscCode.trim().toUpperCase()

      // Basic format validation (4 letters + 7 characters)
      if (!/^[A-Z]{4}[A-Z0-9]{7}$/.test(cleanIFSC)) {
        return false
      }

      // Additional validation rules for common IFSC patterns
      // First 4 characters should be valid bank code letters
      const bankCode = cleanIFSC.substring(0, 4)
      if (!/^[A-Z]{4}$/.test(bankCode)) {
        return false
      }

      // 5th character should be 0 (reserved for future use)
      if (cleanIFSC.charAt(4) !== '0') {
        return false
      }

      // Last 6 characters should be alphanumeric
      const branchCode = cleanIFSC.substring(5)
      if (!/^[A-Z0-9]{6}$/.test(branchCode)) {
        return false
      }

      return true
    } catch (error) {
      console.error('Error validating IFSC:', error)
      return false
    }
  }

  /**
   * Fetch IFSC details with caching (using IPC to main process)
   */
  async fetchIFSCDetails(ifscCode: string): Promise<IFSCDetails | null> {
    try {
      if (!ifscCode) return null

      const cleanIFSC = ifscCode.trim().toUpperCase()

      // Check cache first
      if (this.cache.has(cleanIFSC)) {
        return this.cache.get(cleanIFSC) || null
      }

      // Validate before fetching
      if (!this.validateIFSC(cleanIFSC)) {
        this.cache.set(cleanIFSC, null)
        return null
      }

      // Use IPC to fetch from main process (which can use Node.js packages)
      const details = await window.api.ifsc.fetchDetails(cleanIFSC)

      // Cache the result
      this.cacheResult(cleanIFSC, details)

      return details
    } catch (error) {
      console.error(`Error fetching IFSC details for ${ifscCode}:`, error)
      // Cache null result to avoid repeated API calls for invalid codes
      this.cacheResult(ifscCode.trim().toUpperCase(), null)
      return null
    }
  }

  /**
   * Cache management
   */
  private cacheResult(ifscCode: string, details: IFSCDetails | null): void {
    // Simple LRU cache implementation
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(ifscCode, details)
  }

  /**
   * Enrich bank data for a single transaction
   */
  async enrichTransactionBankData(transaction: TransactionData): Promise<TransactionData> {
    try {
      const receiverIFSC = transaction.receiver_ifsc
      const originalBankName = transaction.receiver_bank || ''
      
      // Create enriched bank data object
      const enrichedBankData: EnrichedBankData = {
        original_bank_name: originalBankName,
        bank_name: originalBankName,
        branch_name: '',
        branch_address: '',
        branch_state: '',
        branch_city: '',
        branch_district: '',
        ifsc_code: receiverIFSC || '',
        is_ifsc_valid: false,
        enrichment_status: 'no_ifsc',
        enriched_at: new Date().toISOString()
      }
      
      if (!receiverIFSC) {
        // No IFSC code available
        enrichedBankData.enrichment_status = 'no_ifsc'
      } else {
        // Validate IFSC
        const isValid = this.validateIFSC(receiverIFSC)
        enrichedBankData.is_ifsc_valid = isValid
        
        if (!isValid) {
          enrichedBankData.enrichment_status = 'invalid_ifsc'
        } else {
          // Fetch IFSC details
          const ifscDetails = await this.fetchIFSCDetails(receiverIFSC)
          
          if (ifscDetails) {
            // Update with enriched data
            enrichedBankData.bank_name = ifscDetails.BANK || originalBankName
            enrichedBankData.branch_name = ifscDetails.BRANCH || ''
            enrichedBankData.branch_address = ifscDetails.ADDRESS || ''
            enrichedBankData.branch_state = ifscDetails.STATE || ''
            enrichedBankData.branch_city = ifscDetails.CITY || ''
            enrichedBankData.branch_district = ifscDetails.DISTRICT || ''
            enrichedBankData.enrichment_status = 'success'
            
            // Replace the receiver_bank field with standardized bank name
            transaction.receiver_bank = ifscDetails.BANK
          } else {
            enrichedBankData.enrichment_status = 'api_error'
          }
        }
      }
      
      // Add enriched bank data to transaction
      transaction.enriched_bank_data = enrichedBankData
      
      return transaction
    } catch (error) {
      console.error('Error enriching transaction bank data:', error)
      
      // Add error information to transaction
      transaction.enriched_bank_data = {
        original_bank_name: transaction.receiver_bank || '',
        bank_name: transaction.receiver_bank || '',
        branch_name: '',
        branch_address: '',
        branch_state: '',
        branch_city: '',
        branch_district: '',
        ifsc_code: transaction.receiver_ifsc || '',
        is_ifsc_valid: false,
        enrichment_status: 'api_error',
        enriched_at: new Date().toISOString()
      }
      
      return transaction
    }
  }

  /**
   * Enrich bank data for multiple transactions
   */
  async enrichTransactionsBankData(transactions: TransactionData[]): Promise<TransactionData[]> {
    const enrichedTransactions: TransactionData[] = []

    // Process transactions in batches to avoid overwhelming the API
    const batchSize = 10
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize)

      // Process batch concurrently
      const enrichedBatch = await Promise.all(
        batch.map(transaction => this.enrichTransactionBankData(transaction))
      )

      enrichedTransactions.push(...enrichedBatch)

      // Add small delay between batches to be respectful to the API
      if (i + batchSize < transactions.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    return enrichedTransactions
  }

  /**
   * Enrich complaint data with IFSC information
   * Updates bank names across layer_transactions, bank_notice_data, and graph_data
   */
  async enrichComplaintData(complaintData: any): Promise<any> {
    try {
      const enrichedData = { ...complaintData }

      // Enrich layer_transactions
      if (enrichedData.layer_transactions) {
        const enrichedLayerTransactions: any = {}

        for (const [layer, transactions] of Object.entries(enrichedData.layer_transactions)) {
          if (Array.isArray(transactions)) {
            enrichedLayerTransactions[layer] = await this.enrichTransactionsBankData(transactions)
          } else {
            enrichedLayerTransactions[layer] = transactions
          }
        }

        enrichedData.layer_transactions = enrichedLayerTransactions
      }

      // Enrich graph_data nodes
      if (enrichedData.graph_data?.nodes) {
        const enrichedNodes = await Promise.all(
          enrichedData.graph_data.nodes.map(async (node: any) => {
            if (node.data?.receiver_ifsc && node.data?.receiver_bank) {
              const ifscDetails = await this.fetchIFSCDetails(node.data.receiver_ifsc)
              if (ifscDetails) {
                return {
                  ...node,
                  data: {
                    ...node.data,
                    receiver_bank: ifscDetails.BANK,
                    bank: ifscDetails.BANK,
                    enriched_bank_data: {
                      original_bank_name: node.data.receiver_bank,
                      bank_name: ifscDetails.BANK,
                      branch_name: ifscDetails.BRANCH || '',
                      branch_address: ifscDetails.ADDRESS || '',
                      branch_state: ifscDetails.STATE || '',
                      branch_city: ifscDetails.CITY || '',
                      branch_district: ifscDetails.DISTRICT || '',
                      ifsc_code: node.data.receiver_ifsc,
                      is_ifsc_valid: true,
                      enrichment_status: 'success',
                      enriched_at: new Date().toISOString()
                    }
                  }
                }
              }
            }
            return node
          })
        )

        enrichedData.graph_data.nodes = enrichedNodes
      }

      // Enrich bank_notice_data
      if (enrichedData.bank_notice_data?.transactions) {
        enrichedData.bank_notice_data.transactions = await this.enrichTransactionsBankData(
          enrichedData.bank_notice_data.transactions
        )
      }

      return enrichedData
    } catch (error) {
      console.error('Error enriching complaint data:', error)
      return complaintData // Return original data if enrichment fails
    }
  }

  /**
   * Get enrichment statistics
   */
  getEnrichmentStats(transactions: TransactionData[]): {
    total: number
    enriched: number
    invalid_ifsc: number
    no_ifsc: number
    api_errors: number
  } {
    const stats = {
      total: transactions.length,
      enriched: 0,
      invalid_ifsc: 0,
      no_ifsc: 0,
      api_errors: 0
    }
    
    transactions.forEach(transaction => {
      const enrichedData = transaction.enriched_bank_data as EnrichedBankData
      if (enrichedData) {
        switch (enrichedData.enrichment_status) {
          case 'success':
            stats.enriched++
            break
          case 'invalid_ifsc':
            stats.invalid_ifsc++
            break
          case 'no_ifsc':
            stats.no_ifsc++
            break
          case 'api_error':
            stats.api_errors++
            break
        }
      }
    })
    
    return stats
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const ifscEnrichmentService = new IFSCEnrichmentService()
export type { EnrichedBankData, TransactionData }
