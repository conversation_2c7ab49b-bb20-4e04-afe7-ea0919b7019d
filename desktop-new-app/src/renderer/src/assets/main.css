@import "tailwindcss";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 210 40% 98%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
 
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* .versions {
  position: absolute;
  bottom: 30px;
  margin: 0 auto;
  padding: 15px 0;
  font-family: 'Menlo', 'Lucida Console', monospace;
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  border-radius: 22px;
  background-color: var(--dark-theme-bg-card);
  backdrop-filter: blur(24px);
} */

code {
  font-weight: 600;
  padding: 3px 5px;
  border-radius: 2px;
  background-color: hsl(var(--card));
  font-family:
    ui-monospace,
    SFMono-Regular,
    SF Mono,
    Menlo,
    Consolas,
    Liberation Mono,
    monospace;
  font-size: 85%;
}

.logo {
  margin-bottom: 20px;
  -webkit-user-drag: none;
  height: 128px;
  width: 128px;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 1.2em hsl(var(--primary)));
}

.creator {
  font-size: 14px;
  line-height: 16px;
  color: hsl(var(--muted-foreground));
  font-weight: 600;
  margin-bottom: 10px;
}

.text {
  font-size: 28px;
  color: hsl(var(--foreground));
  font-weight: 700;
  line-height: 32px;
  text-align: center;
  margin: 0 10px;
  padding: 16px 0;
}

.tip {
  font-size: 16px;
  line-height: 24px;
  color: hsl(var(--muted-foreground));
  font-weight: 600;
}

.react {
  background: -webkit-linear-gradient(315deg, #087ea4 55%, #7c93ee);
  background-clip: text;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.ts {
  background: -webkit-linear-gradient(315deg, #3178c6 45%, #f0dc4e);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.actions {
  display: flex;
  padding-top: 32px;
  margin: -6px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action {
  flex-shrink: 0;
  padding: 6px;
}

.action a {
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  text-align: center;
  font-weight: 600;
  white-space: nowrap;
  border-radius: 20px;
  padding: 0 20px;
  line-height: 38px;
  font-size: 14px;
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
  background-color: hsl(var(--card));
}

.action a:hover {
  border-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  background-color: hsl(var(--secondary));
}

.versions {
  position: absolute;
  bottom: 30px;
  margin: 0 auto;
  padding: 15px 0;
  font-family: 'Menlo', 'Lucida Console', monospace;
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  border-radius: 22px;
  background-color: hsl(var(--card));
  backdrop-filter: blur(24px);
}

.versions li {
  display: block;
  float: left;
  border-right: 1px solid hsl(var(--border));
  padding: 0 20px;
  font-size: 14px;
  line-height: 14px;
  opacity: 0.8;
  &:last-child {
    border: none;
  }
}

/* Glassmorphism utility classes */
.glass-card {
  background: hsl(var(--card) / 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 32px 0 hsl(var(--primary) / 0.15);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid hsl(var(--border));
}

.glass-input {
  background: hsl(var(--card) / 0.5);
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: hsl(var(--foreground));
}

.glass-input:focus {
  outline: 2px solid hsl(var(--accent));
  background: hsl(var(--secondary));
}

.glass-btn {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.4);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: background 0.2s, box-shadow 0.2s;
}

.glass-btn:hover {
  background: hsl(var(--accent));
  box-shadow: 0 4px 16px hsl(var(--primary) / 0.4);
}

/* General spacing and layout improvements */
.space-y-lg > *:not(:last-child) {
  margin-bottom: 1.5rem; /* 24px */
}

.space-x-lg > *:not(:last-child) {
  margin-right: 1.5rem; /* 24px */
}

@media (max-width: 720px) {
  .text {
    font-size: 20px;
  }
}

@media (max-width: 620px) {
  .versions {
    display: none;
  }
}

@media (max-width: 350px) {
  .tip,
  .actions {
    display: none;
  }
}

/* Utility for muted/secondary text */
.muted-text {
  color: hsl(var(--muted-foreground));
}

/* Utility for always-visible headings */
.heading-text {
  color: hsl(var(--foreground));
  font-weight: 700;
}

/* Cyber text gradient for dark theme */
.cyber-text-gradient {
  background: linear-gradient(45deg, #00aaff, #aa00ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Enhanced Glow Effects for UI Components */
.dark\:shadow-glow-blue-sm {
  box-shadow: 0 0 8px rgba(0, 170, 255, 0.3);
}

.dark\:shadow-glow-green-sm {
  box-shadow: 0 0 8px rgba(0, 255, 149, 0.3);
}

/* Enhanced glowing effects for interactive elements */
.glow-hover {
  transition: all 0.3s ease-in-out;
}

.glow-hover:hover {
  transform: translateY(-2px);
}

.dark .glow-hover:hover {
  box-shadow:
    0 0 20px rgba(16, 185, 129, 0.4),
    0 0 40px rgba(16, 185, 129, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.3);
}

.glow-hover:hover {
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.4),
    0 0 40px rgba(59, 130, 246, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Glowing borders for cards */
.glow-border {
  position: relative;
  overflow: hidden;
}

.glow-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(45deg, transparent, rgba(16, 185, 129, 0.5), transparent);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark .glow-border:hover::before {
  opacity: 1;
}

/* Pulsing glow animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.6), 0 0 30px rgba(16, 185, 129, 0.4);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Navbar glow enhancement */
.navbar-glow {
  position: relative;
}

.navbar-glow::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.6), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark .navbar-glow:hover::after {
  opacity: 1;
}

/* Button glow effects */
.btn-glow {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-glow:hover::before {
  left: 100%;
}

/* Table row hover glow */
.table-row-glow {
  transition: all 0.2s ease;
}

.dark .table-row-glow:hover {
  background: rgba(16, 185, 129, 0.05);
  box-shadow: inset 0 0 0 1px rgba(16, 185, 129, 0.2);
}

.table-row-glow:hover {
  background: rgba(59, 130, 246, 0.05);
  box-shadow: inset 0 0 0 1px rgba(59, 130, 246, 0.2);
}

/* Input field glow */
.input-glow {
  transition: all 0.3s ease;
}

.dark .input-glow:focus {
  box-shadow:
    0 0 0 2px rgba(16, 185, 129, 0.3),
    0 0 20px rgba(16, 185, 129, 0.2);
}

.input-glow:focus {
  box-shadow:
    0 0 0 2px rgba(59, 130, 246, 0.3),
    0 0 20px rgba(59, 130, 246, 0.2);
}
