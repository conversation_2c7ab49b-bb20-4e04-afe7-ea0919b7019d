import React, { useCallback, useEffect, useState, Suspense, lazy } from 'react'
import {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  Panel,
  ReactFlowProvider,
  useReactFlow,
  ReactFlow,
  BackgroundVariant
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { useParams, useNavigate } from 'react-router-dom'
import { FiArrowLeft, FiDownload, FiPlus } from 'react-icons/fi'
import { toPng } from 'html-to-image'
import { v4 as uuidv4 } from 'uuid'

import { useThemeContext } from '../context/useThemeContext'
import { TailwindAlertProvider } from '../context/TailwindAlertContext'
import { useAlert } from '../hooks/useAlert'
import { NodeData, EdgeData, GraphNode } from '../../../shared/api.d'
import { useGraphData } from '../hooks/useComplaintData'

// Define types for raw graph data
interface RawGraphNode {
  id: string
  type: string
  label?: string
  account?: string
  layer?: number
  amount?: string
  date?: string
  bank?: string
  fraud_type?: string
  [key: string]: unknown
}

interface RawGraphEdge {
  id: string
  source: string
  target: string
  type?: string
  [key: string]: unknown
}

// Lazy load custom nodes if they become complex
const TransactionNode = lazy(() => import('../components/graph/nodes/TransactionNode'))
const MetadataNode = lazy(() => import('../components/graph/nodes/MetadataNode'))

const nodeTypes = {
  transaction: TransactionNode,
  metadata: MetadataNode,
  sender_account: TransactionNode,
  receiver_account: TransactionNode,
  default: TransactionNode
}

const edgeTypes = {
  // Define custom edge types here if needed
}

const GraphVisualizationContent: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { isDark: isDarkMode } = useThemeContext()
  const { showError, showSuccess } = useAlert()
  const { fitView } = useReactFlow()

  // Ensure id is not undefined before passing to useGraphData
  console.log('[GraphVisualization] Component rendered with id:', id)
  const { graphData: complaintGraphData, loading, error } = useGraphData(id || '')

  console.log('[GraphVisualization] Hook state:', {
    graphData: complaintGraphData,
    loading,
    error,
    hasNodes: complaintGraphData?.nodes?.length,
    hasEdges: complaintGraphData?.edges?.length
  })

  const [nodes, setNodes, onNodesChange] = useNodesState<Node<NodeData>>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge<EdgeData>>([])
  const [isEraserMode, setIsEraserMode] = useState(false)

  // Field selection state
  const [visibleFields, setVisibleFields] = useState<Set<string>>(
    new Set(['account', 'amount', 'txn_type', 'date', 'bank'])
  )
  const [showFieldPanel, setShowFieldPanel] = useState(false)

  // Available fields for selection
  const availableFields = [
    { key: 'account', label: 'Account Number' },
    { key: 'amount', label: 'Amount' },
    { key: 'txn_type', label: 'Transaction Type' },
    { key: 'date', label: 'Date' },
    { key: 'bank', label: 'Bank' },
    { key: 'txn_id', label: 'Transaction ID' },
    { key: 'reference', label: 'Reference' },
    { key: 'receiver_info', label: 'Receiver Info' },
    { key: 'fraud_type', label: 'Fraud Type' },
    { key: 'layer', label: 'Layer' }
  ]

  // Layer-based layout function for proper hierarchy
  const applyDagreLayout = useCallback(
    (nodesToLayout: GraphNode[], edgesToLayout: Edge<EdgeData>[]): Node<NodeData>[] => {
      const nodeWidth = 350
      const layerSpacing = 250
      const nodeSpacing = 100

      // Group nodes by layer
      const nodesByLayer = new Map<number, Node<NodeData>[]>()

      nodesToLayout.forEach((node) => {
        const layer = node.data?.layer || 0
        if (!nodesByLayer.has(layer)) {
          nodesByLayer.set(layer, [])
        }
        nodesByLayer.get(layer)!.push(node as Node<NodeData>)
      })

      // Sort layers
      const sortedLayers = Array.from(nodesByLayer.keys()).sort((a, b) => a - b)

      const layoutedNodes: Node<NodeData>[] = []

      sortedLayers.forEach((layerNum, layerIndex) => {
        const layerNodes = nodesByLayer.get(layerNum)!
        const layerY = layerIndex * layerSpacing + 100

        // For layer 0 (sender nodes), position at top
        // For layer 1+ (receiver nodes), position below their parents
        if (layerNum === 0) {
          // Layer 0: Sender nodes (fraud source) at the top
          layerNodes.forEach((node, nodeIndex) => {
            const x = (nodeIndex - (layerNodes.length - 1) / 2) * (nodeWidth + nodeSpacing)
            layoutedNodes.push({
              ...node,
              position: { x, y: layerY }
            })
          })
        } else {
          // Layer 1+: Position receiver nodes below their parent nodes
          layerNodes.forEach((node, nodeIndex) => {
            // Try to find parent node from previous layer
            const parentEdge = edgesToLayout.find((edge) => edge.target === node.id)
            let x = nodeIndex * (nodeWidth + nodeSpacing)

            if (parentEdge) {
              // Find parent node position
              const parentNode = layoutedNodes.find((n) => n.id === parentEdge.source)
              if (parentNode) {
                // Position child node below parent with slight offset
                x = parentNode.position.x + (nodeIndex % 2 === 0 ? -50 : 50)
              }
            }

            layoutedNodes.push({
              ...node,
              position: { x, y: layerY }
            })
          })
        }
      })

      return layoutedNodes
    },
    []
  )

  // Process raw graph data into proper hierarchical structure
  const processGraphData = useCallback(
    (rawGraphData: { nodes?: unknown[]; edges?: unknown[] }) => {
      console.log('[GraphVisualization] Processing raw graph data:', rawGraphData)

      if (!rawGraphData?.nodes || !rawGraphData?.edges) {
        console.log('[GraphVisualization] Invalid graph data structure')
        return { nodes: [], edges: [] }
      }

      // Convert raw nodes to ReactFlow format with proper type mapping
      const processedNodes = rawGraphData.nodes.map((nodeData) => {
        const node = nodeData as RawGraphNode
        // Determine the proper ReactFlow node type
        let reactFlowNodeType = 'transaction'
        if (node.type === 'metadata') {
          reactFlowNodeType = 'metadata'
        }

        // Create proper node data structure with consolidated transaction support
        const processedNodeData = {
          ...node, // Include all original properties first
          label: node.label || node.bank || node.id,
          account: node.account || node.id,
          nodeType: node.type, // Rename to avoid conflict
          layer: node.layer || 0,
          amount: node.amount,
          date: node.date,
          bank: node.bank || node.sender_bank || node.receiver_bank,
          fraud_type: node.fraud_type,
          // Correct the node type mapping based on backend structure
          isSenderNode: node.type === 'sender_account' && (node.layer || 0) === 0,
          isMainTransaction: node.type === 'receiver_account' && (node.layer || 0) >= 1,
          isVictim: node.type === 'sender_account' && (node.layer || 0) === 0,
          // Handle consolidated transactions
          hasSubTransactions: node.hasSubTransactions || false,
          consolidatedTransactions: node.consolidatedTransactions || [],
          transaction_count: node.transaction_count || 1,
          total_amount: node.total_amount || node.amount,
          // Additional transaction details
          txn_id: node.txn_id || node.transaction_id,
          transaction_id: node.transaction_id || node.txn_id,
          txn_type: node.txn_type,
          reference: node.reference,
          receiver_info: node.receiver_info,
          sender_bank: node.sender_bank,
          receiver_bank: node.receiver_bank,
          receiver_ifsc: node.receiver_ifsc,
          // Field visibility for filtering
          visibleFields: visibleFields
        }

        return {
          id: node.id,
          type: reactFlowNodeType,
          data: processedNodeData,
          position: { x: 0, y: 0 } // Will be set by layout
        }
      })

      // Convert raw edges to ReactFlow format
      const processedEdges = rawGraphData.edges.map((edgeData) => {
        const edge = edgeData as RawGraphEdge
        return {
          id: edge.id,
          source: edge.source,
          target: edge.target,
          type: 'default',
          data: edge
        }
      })

      console.log('[GraphVisualization] Processed nodes:', processedNodes.length)
      console.log('[GraphVisualization] Processed edges:', processedEdges.length)

      return { nodes: processedNodes, edges: processedEdges }
    },
    [visibleFields]
  )

  useEffect(() => {
    if (complaintGraphData) {
      const { nodes: processedNodes, edges: processedEdges } = processGraphData(complaintGraphData)

      if (processedNodes.length > 0) {
        const layoutedNodes = applyDagreLayout(processedNodes, processedEdges)

        setNodes(layoutedNodes)
        setEdges(processedEdges)

        // Fit view after nodes are set and laid out
        requestAnimationFrame(() => {
          fitView()
        })
      }
    }
  }, [complaintGraphData, setNodes, setEdges, fitView, applyDagreLayout, processGraphData])

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: Edge<EdgeData> = {
        id: uuidv4(),
        source: params.source,
        target: params.target,
        type: 'default',
        data: {}
      }
      setEdges((eds) => addEdge(newEdge, eds))
      showSuccess('Edge added successfully!')
    },
    [setEdges, showSuccess]
  )

  const onNodesDelete = useCallback(
    (deleted: Node<NodeData>[]) => {
      setNodes((nds) => nds.filter((node) => !deleted.some((d) => d.id === node.id)))
      setEdges((eds) =>
        eds.filter((edge) => !deleted.some((d) => d.id === edge.source || d.id === edge.target))
      )
      showSuccess(`Deleted ${deleted.length} nodes.`)
    },
    [setNodes, setEdges, showSuccess]
  )

  // Handle key press for node deletion
  const onKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === 'Delete' || event.key === 'Backspace') {
        const selectedNodes = nodes.filter((node) => node.selected)
        if (selectedNodes.length > 0) {
          onNodesDelete(selectedNodes)
        }
      }
    },
    [nodes, onNodesDelete]
  )

  // Add keyboard event listener
  useEffect(() => {
    document.addEventListener('keydown', onKeyDown)
    return () => {
      document.removeEventListener('keydown', onKeyDown)
    }
  }, [onKeyDown])

  // Handle node click for eraser mode
  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node<NodeData>) => {
      if (isEraserMode) {
        // Delete the clicked node in eraser mode
        setNodes((nds) => nds.filter((n) => n.id !== node.id))
        setEdges((eds) => eds.filter((edge) => edge.source !== node.id && edge.target !== node.id))
        showSuccess(`Node ${node.id} deleted`)
      }
    },
    [isEraserMode, setNodes, setEdges, showSuccess]
  )

  // Toggle eraser mode
  const toggleEraserMode = useCallback(() => {
    setIsEraserMode(!isEraserMode)
    showSuccess(
      isEraserMode ? 'Eraser mode disabled' : 'Eraser mode enabled - click nodes to delete'
    )
  }, [isEraserMode, showSuccess])

  const onAddNode = useCallback(() => {
    const newNode: Node<NodeData> = {
      id: uuidv4(),
      position: { x: Math.random() * 500, y: Math.random() * 500 },
      data: { label: 'New Node', layer: 0 },
      type: 'default'
    }
    setNodes((nds) => nds.concat(newNode))
    showSuccess('Added new node.')
  }, [setNodes, showSuccess])

  const onDownloadImage = useCallback(() => {
    const flowContainer = document.querySelector('.react-flow')
    if (flowContainer) {
      // Hide controls and minimap temporarily for cleaner export
      const controls = document.querySelector('.react-flow__controls')
      const minimap = document.querySelector('.react-flow__minimap')
      const panel = document.querySelector('.react-flow__panel')

      const originalControlsDisplay = controls ? (controls as HTMLElement).style.display : ''
      const originalMinimapDisplay = minimap ? (minimap as HTMLElement).style.display : ''
      const originalPanelDisplay = panel ? (panel as HTMLElement).style.display : ''

      if (controls) (controls as HTMLElement).style.display = 'none'
      if (minimap) (minimap as HTMLElement).style.display = 'none'
      if (panel) (panel as HTMLElement).style.display = 'none'

      toPng(flowContainer as HTMLElement, {
        backgroundColor: isDarkMode ? '#0a0a0f' : '#f7f7f7',
        width: flowContainer.clientWidth,
        height: flowContainer.clientHeight,
        style: {
          width: flowContainer.clientWidth + 'px',
          height: flowContainer.clientHeight + 'px'
        }
      })
        .then((dataUrl) => {
          // Restore controls and minimap
          if (controls) (controls as HTMLElement).style.display = originalControlsDisplay
          if (minimap) (minimap as HTMLElement).style.display = originalMinimapDisplay
          if (panel) (panel as HTMLElement).style.display = originalPanelDisplay

          const a = document.createElement('a')
          a.setAttribute('download', `graph-${id}.png`)
          a.setAttribute('href', dataUrl)
          a.click()
          showSuccess('Graph image downloaded successfully!')
        })
        .catch((err) => {
          // Restore controls and minimap on error
          if (controls) (controls as HTMLElement).style.display = originalControlsDisplay
          if (minimap) (minimap as HTMLElement).style.display = originalMinimapDisplay
          if (panel) (panel as HTMLElement).style.display = originalPanelDisplay

          showError('Failed to download image: ' + err.message)
        })
    } else {
      showError('Could not find graph container for download.')
    }
  }, [id, isDarkMode, showError, showSuccess])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-4 text-lg ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Loading graph data...
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className={`text-red-500 text-xl mb-4`}>Error loading graph data</div>
          <p className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            <FiArrowLeft /> Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div
        className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b px-4 py-3 flex items-center justify-between`}
      >
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/dashboard')}
            className={`flex items-center gap-2 px-3 py-2 rounded ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'}`}
          >
            <FiArrowLeft />
            Back to Dashboard
          </button>
          <h1 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Graph Analysis - Complaint {id}
          </h1>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={toggleEraserMode}
            className={`flex items-center gap-2 px-3 py-2 rounded ${
              isEraserMode
                ? isDarkMode
                  ? 'bg-red-700 hover:bg-red-600 text-white'
                  : 'bg-red-500 hover:bg-red-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 hover:bg-gray-600 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
            }`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
            {isEraserMode ? 'Exit Eraser' : 'Eraser Mode'}
          </button>
          <button
            onClick={onAddNode}
            className={`flex items-center gap-2 px-3 py-2 rounded ${isDarkMode ? 'bg-green-700 hover:bg-green-600 text-white' : 'bg-green-500 hover:bg-green-600 text-white'}`}
          >
            <FiPlus />
            Add Node
          </button>
          <button
            onClick={onDownloadImage}
            className={`flex items-center gap-2 px-3 py-2 rounded ${isDarkMode ? 'bg-blue-700 hover:bg-blue-600 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white'}`}
          >
            <FiDownload />
            Download Image
          </button>
        </div>
      </div>

      {/* React Flow Container - Full width and height */}
      <div className="flex-1 w-full h-full relative reactflow-wrapper">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodesDelete={onNodesDelete}
          onNodeClick={onNodeClick}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          deleteKeyCode={['Delete', 'Backspace']}
          multiSelectionKeyCode="Shift"
          selectionKeyCode="Shift"
          style={{ cursor: isEraserMode ? 'crosshair' : 'default' }}
          fitView
        >
          <MiniMap />
          <Controls />
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          <Panel position="top-left" className="p-2 space-y-2">
            <div className="flex flex-col gap-2">
              <button
                onClick={() => fitView()}
                className={`px-3 py-2 rounded text-sm ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'}`}
              >
                Fit View
              </button>

              {/* Node Status Indicators */}
              <div
                className={`p-2 rounded text-xs ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} border`}
              >
                <div className="font-semibold mb-1">Graph Stats:</div>
                <div>Nodes: {nodes.length}</div>
                <div>Edges: {edges.length}</div>
                <div>Selected: {nodes.filter((n) => n.selected).length}</div>
              </div>

              {/* Field Selection Panel */}
              <div
                className={`p-2 rounded text-xs ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} border`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-semibold">Field Display:</div>
                  <button
                    onClick={() => setShowFieldPanel(!showFieldPanel)}
                    className={`px-2 py-1 rounded text-xs ${isDarkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                  >
                    {showFieldPanel ? 'Hide' : 'Show'}
                  </button>
                </div>

                {showFieldPanel && (
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {availableFields.map((field) => (
                      <label key={field.key} className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={visibleFields.has(field.key)}
                          onChange={(e) => {
                            const newFields = new Set(visibleFields)
                            if (e.target.checked) {
                              newFields.add(field.key)
                            } else {
                              newFields.delete(field.key)
                            }
                            setVisibleFields(newFields)
                          }}
                          className="w-3 h-3"
                        />
                        <span className="text-xs">{field.label}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </Panel>
        </ReactFlow>
      </div>
    </div>
  )
}

const GraphVisualization: React.FC = () => (
  <TailwindAlertProvider>
    <ReactFlowProvider>
      <Suspense fallback={<div>Loading Graph...</div>}>
        <GraphVisualizationContent />
      </Suspense>
    </ReactFlowProvider>
  </TailwindAlertProvider>
)

export default GraphVisualization
